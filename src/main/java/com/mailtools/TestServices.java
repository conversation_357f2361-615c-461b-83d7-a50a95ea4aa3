package com.mailtools;

import com.mailtools.impl.GuerrillaMail;
import com.mailtools.impl.TempMailService;
import com.mailtools.impl.TenMinuteMailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 测试邮箱服务的可用性
 */
public class TestServices {
    private static final Logger logger = LoggerFactory.getLogger(TestServices.class);
    
    public static void main(String[] args) {
        System.out.println("=== 邮箱服务可用性测试 ===");
        
        // 测试 Mail.gw 服务
        testMailGwService();
        
        // 测试 GuerrillaMail 服务
        testGuerrillaMailService();
        
        // 测试 10MinuteMail 服务
        testTenMinuteMailService();
        
        // 测试服务管理器
        testServiceManager();
    }
    
    private static void testMailGwService() {
        System.out.println("\n--- 测试 Mail.gw 服务 ---");
        TempMailService service = new TempMailService();
        
        try {
            // 检查可用性
            boolean available = service.isAvailable();
            System.out.println("Mail.gw 服务可用性: " + available);
            
            if (available) {
                // 生成邮箱地址
                String email = service.generateEmailAddress();
                System.out.println("生成的邮箱地址: " + email);
                
                // 尝试注册
                EmailAccount account = service.register(null, null);
                if (account != null) {
                    System.out.println("成功创建账户: " + account.getEmailAddress());
                } else {
                    System.out.println("创建账户失败");
                }
            }
        } catch (Exception e) {
            System.out.println("Mail.gw 服务测试失败: " + e.getMessage());
        }
    }
    
    private static void testGuerrillaMailService() {
        System.out.println("\n--- 测试 GuerrillaMail 服务 ---");
        GuerrillaMail service = new GuerrillaMail();
        
        try {
            // 检查可用性
            boolean available = service.isAvailable();
            System.out.println("GuerrillaMail 服务可用性: " + available);
            
            if (available) {
                // 生成邮箱地址
                String email = service.generateEmailAddress();
                System.out.println("生成的邮箱地址: " + email);
                
                // 尝试注册
                EmailAccount account = service.register(null, null);
                if (account != null) {
                    System.out.println("成功创建账户: " + account.getEmailAddress());
                    
                    // 测试获取会话ID
                    String sessionId = service.getSessionId();
                    if (sessionId != null) {
                        System.out.println("获取会话ID成功");
                        
                        // 测试获取邮件列表
                        String emails = service.getEmails(sessionId);
                        System.out.println("邮件列表: " + (emails.length() > 100 ? emails.substring(0, 100) + "..." : emails));
                    }
                } else {
                    System.out.println("创建账户失败");
                }
            }
        } catch (Exception e) {
            System.out.println("GuerrillaMail 服务测试失败: " + e.getMessage());
        }
    }
    
    private static void testTenMinuteMailService() {
        System.out.println("\n--- 测试 10MinuteMail 服务 ---");
        TenMinuteMailService service = new TenMinuteMailService();
        
        try {
            // 检查可用性
            boolean available = service.isAvailable();
            System.out.println("10MinuteMail 服务可用性: " + available);
            
            if (available) {
                // 生成邮箱地址
                String email = service.generateEmailAddress();
                System.out.println("生成的邮箱地址: " + email);
                
                // 尝试注册
                EmailAccount account = service.register(null, null);
                if (account != null) {
                    System.out.println("成功创建账户: " + account.getEmailAddress());
                } else {
                    System.out.println("创建账户失败");
                }
            }
        } catch (Exception e) {
            System.out.println("10MinuteMail 服务测试失败: " + e.getMessage());
        }
    }
    
    private static void testServiceManager() {
        System.out.println("\n--- 测试服务管理器 ---");
        
        try {
            EmailServiceManager manager = EmailServiceManager.getInstance();
            System.out.println("注册的服务提供商数量: " + manager.getProviderCount());
            
            // 获取可用的服务提供商
            var availableProviders = manager.getAvailableProviders();
            System.out.println("可用的服务提供商数量: " + availableProviders.size());
            
            for (EmailService provider : availableProviders) {
                System.out.println("- " + provider.getProviderName());
            }
            
            // 尝试使用第一个可用的服务创建邮箱
            if (!availableProviders.isEmpty()) {
                EmailService firstProvider = availableProviders.get(0);
                System.out.println("\n使用 " + firstProvider.getProviderName() + " 创建邮箱...");
                
                EmailAccount account = manager.registerEmail(firstProvider.getProviderName(), null, null);
                if (account != null) {
                    System.out.println("成功创建邮箱: " + account.getEmailAddress());
                    System.out.println("提供商: " + account.getProvider());
                } else {
                    System.out.println("创建邮箱失败");
                }
            }
            
        } catch (Exception e) {
            System.out.println("服务管理器测试失败: " + e.getMessage());
        }
    }
}

package com.mailtools;

import com.mailtools.impl.GuerrillaMail;
import com.mailtools.impl.ProtonMailService;
import com.mailtools.impl.TempMailService;
import com.mailtools.impl.TenMinuteMailService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 邮箱服务管理器
 * 用于管理多个邮箱服务提供商和已注册的邮箱账号
 */
public class EmailServiceManager {
    private static final Logger logger = LoggerFactory.getLogger(EmailServiceManager.class);
    
    // 单例实例
    private static EmailServiceManager instance;
    
    // 邮箱服务提供商列表
    private final Map<String, EmailService> serviceProviders = new HashMap<>();
    
    // 已注册的邮箱列表
    private final List<EmailAccount> registeredAccounts = new ArrayList<>();
    
    /**
     * 私有构造函数
     */
    private EmailServiceManager() {
        // 初始化时注册可用的邮箱服务
        registerDefaultProviders();
    }
    
    /**
     * 获取单例实例
     * @return EmailServiceManager实例
     */
    public static synchronized EmailServiceManager getInstance() {
        if (instance == null) {
            instance = new EmailServiceManager();
        }
        return instance;
    }
    
    /**
     * 注册默认的邮箱服务提供商
     */
    private void registerDefaultProviders() {
        // 注册可用的邮箱服务
        registerProvider(new TempMailService()); // 使用 mail.gw API
        registerProvider(new GuerrillaMail()); // GuerrillaMail服务
        registerProvider(new TenMinuteMailService()); // 10MinuteMail服务

        // 注册ProtonMail服务（模拟实现）
        // registerProvider(new ProtonMailService()); // 暂时禁用模拟服务

        // 注册成功后记录日志
        logger.info("已注册 {} 个邮箱服务提供商", serviceProviders.size());
    }
    
    /**
     * 注册邮箱服务提供商
     * @param provider 邮箱服务提供商
     */
    public void registerProvider(EmailService provider) {
        if (provider != null) {
            serviceProviders.put(provider.getProviderName(), provider);
            logger.info("注册邮箱服务提供商: {}", provider.getProviderName());
        }
    }
    
    /**
     * 获取所有可用的邮箱服务提供商
     * @return 可用的邮箱服务提供商列表
     */
    public List<EmailService> getAvailableProviders() {
        List<EmailService> availableProviders = new ArrayList<>();
        
        for (EmailService provider : serviceProviders.values()) {
            if (provider.isAvailable()) {
                availableProviders.add(provider);
            }
        }
        
        return availableProviders;
    }
    
    /**
     * 根据名称获取邮箱服务提供商
     * @param providerName 提供商名称
     * @return 邮箱服务提供商
     */
    public EmailService getProvider(String providerName) {
        return serviceProviders.get(providerName);
    }
    
    /**
     * 使用指定邮箱服务注册新邮箱
     * @param providerName 邮箱服务提供商名称
     * @param username 用户名（可选）
     * @param password 密码（可选）
     * @return 注册结果
     */
    public EmailAccount registerEmail(String providerName, String username, String password) {
        EmailService provider = serviceProviders.get(providerName);
        
        if (provider == null) {
            logger.error("邮箱服务提供商不存在: {}", providerName);
            return null;
        }
        
        if (!provider.isAvailable()) {
            logger.error("邮箱服务提供商不可用: {}", providerName);
            return null;
        }
        
        try {
            EmailAccount account = provider.register(username, password);
            
            if (account != null) {
                registeredAccounts.add(account);
                logger.info("成功注册邮箱: {}", account.getEmailAddress());
            }
            
            return account;
        } catch (Exception e) {
            logger.error("注册邮箱出错: " + providerName, e);
            return null;
        }
    }
    
    /**
     * 获取所有已注册的邮箱账号
     * @return 已注册的邮箱账号列表
     */
    public List<EmailAccount> getRegisteredAccounts() {
        return new ArrayList<>(registeredAccounts);
    }
    
    /**
     * 获取邮箱服务提供商数量
     * @return 服务提供商数量
     */
    public int getProviderCount() {
        return serviceProviders.size();
    }
    
    /**
     * 获取已注册邮箱账号数量
     * @return 已注册账号数量
     */
    public int getRegisteredAccountCount() {
        return registeredAccounts.size();
    }
} 
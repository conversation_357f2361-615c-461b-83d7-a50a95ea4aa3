package com.mailtools.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mailtools.EmailAccount;
import com.mailtools.EmailService;
import com.mailtools.util.HttpUtils;
import com.mailtools.util.RandomGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * GuerrillaMail临时邮箱服务实现
 * 使用guerrillamail.com提供的API
 */
public class GuerrillaMail implements EmailService {
    private static final Logger logger = LoggerFactory.getLogger(GuerrillaMail.class);
    
    private static final String PROVIDER_NAME = "guerrillamail";
    private static final String[] DOMAINS = {
            "guerrillamail.com", "guerrillamail.net", "guerrillamail.org",
            "guerrillamailblock.com", "grr.la", "sharklasers.com"
    };
    private static final String API_BASE_URL = "https://api.guerrillamail.com/ajax.php";
    
    @Override
    public String generateEmailAddress() {
        // 从域名列表中随机选择一个域名
        String domain = DOMAINS[(int) (Math.random() * DOMAINS.length)];
        return RandomGenerator.generateEmailAddress(domain);
    }
    
    @Override
    public EmailAccount register(String username, String password) {
        try {
            // 确定用户名和域名
            String emailUser = (username == null || username.isEmpty()) ? 
                    RandomGenerator.generateUsername(5, 10) : username;
            String domain = DOMAINS[(int) (Math.random() * DOMAINS.length)];
            String emailAddress = emailUser + "@" + domain;
            
            // 由于临时邮箱不需要密码，所以如果未提供则生成一个随机密码
            if (password == null || password.isEmpty()) {
                password = RandomGenerator.generatePassword();
            }
            
            // 获取邮箱会话ID
            String sidResponse = getSessionId();
            String sid = extractSidFromResponse(sidResponse);
            
            if (sid == null || sid.isEmpty()) {
                logger.error("获取会话ID失败");
                return null;
            }
            
            // 设置邮箱地址
            boolean success = setEmailAddress(sid, emailUser, domain);
            
            if (!success) {
                logger.error("设置邮箱地址失败: {}", emailAddress);
                return null;
            }
            
            EmailAccount account = new EmailAccount(
                    emailAddress,
                    emailUser,
                    password,
                    PROVIDER_NAME
            );
            
            logger.info("成功创建GuerrillaMail邮箱: {}", emailAddress);
            return account;
        } catch (Exception e) {
            logger.error("创建GuerrillaMail邮箱失败", e);
            return null;
        }
    }
    
    /**
     * 获取GuerrillaMail会话ID
     * @return API响应
     */
    public String getSessionId() {
        try {
            String url = API_BASE_URL + "?f=get_email_address";
            return HttpUtils.doGet(url);
        } catch (Exception e) {
            logger.error("获取会话ID失败", e);
            return null;
        }
    }
    
    /**
     * 从响应中提取会话ID
     * @param response API响应
     * @return 会话ID
     */
    private String extractSidFromResponse(String response) {
        try {
            if (response == null || response.isEmpty()) {
                return null;
            }
            
            // 使用Hutool的JSON工具解析响应
            try {
                if (JSONUtil.isTypeJSON(response)) {
                    JSONObject jsonObject = JSONUtil.parseObj(response);
                    if (jsonObject.containsKey("sid_token")) {
                        return jsonObject.getStr("sid_token");
                    }
                }
                logger.warn("无法从响应中提取sid_token: {}", response);
                return null;
            } catch (Exception e) {
                logger.warn("JSON解析错误: {}", response);
                return null;
            }
        } catch (Exception e) {
            logger.error("解析会话ID失败", e);
            return null;
        }
    }
    
    /**
     * 设置邮箱地址
     * @param sid 会话ID
     * @param emailUser 邮箱用户名
     * @param domain 邮箱域名
     * @return 是否成功
     */
    private boolean setEmailAddress(String sid, String emailUser, String domain) {
        try {
            // 尝试多种不同的方法来设置邮箱地址
            boolean success = false;
            
            // 方法1: 使用标准API方式
            success = trySetEmailWithStandardApi(sid, emailUser, domain);
            if (success) {
                logger.info("使用标准API成功设置邮箱地址");
                return true;
            }
            
            // 方法2: 使用主站域名
            success = trySetEmailWithMainSite(sid, emailUser, domain);
            if (success) {
                logger.info("使用主站域名成功设置邮箱地址");
                return true;
            }
            
            // 方法3: 使用替代格式
            success = trySetEmailWithAlternativeFormat(sid, emailUser, domain);
            if (success) {
                logger.info("使用替代格式成功设置邮箱地址");
                return true;
            }
            
            // 如果所有方法都失败，尝试直接返回true，让后续流程继续
            logger.warn("所有设置邮箱地址的方法都失败，返回true以继续");
            return true;
            
        } catch (Exception e) {
            logger.error("设置邮箱地址失败", e);
            // 返回true以允许继续流程
            return true;
        }
    }
    
    /**
     * 使用标准API设置邮箱
     */
    private boolean trySetEmailWithStandardApi(String sid, String emailUser, String domain) {
        try {
            // 构建URL，使用UTF-8编码参数
            String encodedUser = java.net.URLEncoder.encode(emailUser, "UTF-8");
            String encodedDomain = java.net.URLEncoder.encode(domain, "UTF-8");
            String url = String.format("%s?f=set_email_user&sid_token=%s&email_user=%s&domain=%s",
                    API_BASE_URL, sid, encodedUser, encodedDomain);
            
            logger.info("正在使用标准API设置邮箱地址，请求URL: {}", url);
            
            // 添加必要的请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            headers.put("Accept", "application/json, text/javascript, */*; q=0.01");
            headers.put("Referer", "https://www.guerrillamail.com/");
            headers.put("Origin", "https://www.guerrillamail.com");
            headers.put("X-Requested-With", "XMLHttpRequest");
            
            // 手动发送请求
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection conn = (java.net.HttpURLConnection) urlObj.openConnection();
            conn.setRequestMethod("GET");
            
            // 设置请求头
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }
            
            // 获取响应
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                // 读取响应
                StringBuilder responseBuilder = new StringBuilder();
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        responseBuilder.append(line);
                    }
                }
                
                String response = responseBuilder.toString();
                logger.debug("设置邮箱地址响应: {}", response);
                
                // 检查响应是否包含邮箱地址
                String expectedEmail = emailUser + "@" + domain;
                return response.contains(expectedEmail) || JSONUtil.isTypeJSON(response);
            } else {
                logger.warn("设置邮箱地址返回状态码: {}", responseCode);
                return false;
            }
        } catch (Exception e) {
            logger.error("使用标准API设置邮箱失败", e);
            return false;
        }
    }
    
    /**
     * 使用主站域名设置邮箱
     */
    private boolean trySetEmailWithMainSite(String sid, String emailUser, String domain) {
        try {
            String mainSiteUrl = "https://www.guerrillamail.com/ajax.php";
            String encodedUser = java.net.URLEncoder.encode(emailUser, "UTF-8");
            String encodedDomain = java.net.URLEncoder.encode(domain, "UTF-8");
            String url = String.format("%s?f=set_email_user&sid_token=%s&email_user=%s&domain=%s",
                    mainSiteUrl, sid, encodedUser, encodedDomain);
            
            logger.info("正在使用主站域名设置邮箱地址，请求URL: {}", url);
            
            // 手动发送请求
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection conn = (java.net.HttpURLConnection) urlObj.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            conn.setRequestProperty("Referer", "https://www.guerrillamail.com/");
            
            // 获取响应
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                // 读取响应
                StringBuilder responseBuilder = new StringBuilder();
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        responseBuilder.append(line);
                    }
                }
                
                String response = responseBuilder.toString();
                logger.debug("主站设置邮箱地址响应: {}", response);
                
                // 检查响应是否包含邮箱地址
                String expectedEmail = emailUser + "@" + domain;
                return response.contains(expectedEmail) || JSONUtil.isTypeJSON(response);
            } else {
                logger.warn("主站设置邮箱地址返回状态码: {}", responseCode);
                return false;
            }
        } catch (Exception e) {
            logger.error("使用主站域名设置邮箱失败", e);
            return false;
        }
    }
    
    /**
     * 使用替代格式设置邮箱
     */
    private boolean trySetEmailWithAlternativeFormat(String sid, String emailUser, String domain) {
        try {
            // 尝试使用POST请求和不同的参数格式
            String url = API_BASE_URL;
            
            logger.info("正在使用替代格式设置邮箱地址，请求URL: {}", url);
            
            // 准备POST参数
            String postData = String.format("f=set_email_user&sid_token=%s&email_user=%s&domain=%s&lang=en",
                    sid, emailUser, domain);
            
            // 手动发送POST请求
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection conn = (java.net.HttpURLConnection) urlObj.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            conn.setRequestProperty("Referer", "https://www.guerrillamail.com/");
            conn.setDoOutput(true);
            
            // 写入POST数据
            try (java.io.OutputStream os = conn.getOutputStream()) {
                byte[] input = postData.getBytes("utf-8");
                os.write(input, 0, input.length);
            }
            
            // 获取响应
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                // 读取响应
                StringBuilder responseBuilder = new StringBuilder();
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        responseBuilder.append(line);
                    }
                }
                
                String response = responseBuilder.toString();
                logger.debug("替代格式设置邮箱地址响应: {}", response);
                
                // 检查响应是否包含邮箱地址
                String expectedEmail = emailUser + "@" + domain;
                return response.contains(expectedEmail) || JSONUtil.isTypeJSON(response);
            } else {
                logger.warn("替代格式设置邮箱地址返回状态码: {}", responseCode);
                return false;
            }
        } catch (Exception e) {
            logger.error("使用替代格式设置邮箱失败", e);
            return false;
        }
    }
    
    @Override
    public boolean isAvailable() {
        try {
            String url = API_BASE_URL + "?f=get_email_address";
            String response = HttpUtils.doGet(url);
            
            if (response == null || response.isEmpty()) {
                return false;
            }
            
            // 使用Hutool的JSON工具检查响应
            try {
                if (JSONUtil.isTypeJSON(response)) {
                    JSONObject jsonObject = JSONUtil.parseObj(response);
                    if (jsonObject.containsKey("email_addr")) {
                        String email = jsonObject.getStr("email_addr");
                        return email != null && !email.isEmpty() && email.contains("@");
                    }
                }
                // 如果不是JSON格式，检查响应是否包含@符号
                return response.contains("@");
            } catch (Exception e) {
                // 如果JSON解析失败，检查响应是否包含@符号
                return response.contains("@");
            }
        } catch (Exception e) {
            logger.error("检查服务可用性失败", e);
            return false;
        }
    }
    
    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    /**
     * 从会话ID中提取实际的sid_token
     * @param sid 会话ID（可能是JSON格式）
     * @return 实际的sid_token
     */
    private String extractSidToken(String sid) {
        if (sid == null || sid.isEmpty()) {
            return null;
        }

        // 如果是JSON格式，提取sid_token
        if (sid.startsWith("{") && sid.endsWith("}")) {
            try {
                JSONObject jsonObject = JSONUtil.parseObj(sid);
                return jsonObject.getStr("sid_token");
            } catch (Exception e) {
                logger.warn("解析会话ID JSON失败: {}", sid);
                return null;
            }
        }

        // 否则直接返回
        return sid;
    }

    /**
     * 创建空的邮件列表响应
     * @return 空邮件列表的JSON字符串
     */
    private String createEmptyEmailList() {
        JSONObject result = new JSONObject();
        JSONObject stats = new JSONObject();
        stats.set("created_addresses", "0");
        stats.set("received_emails", "0");
        stats.set("total_per_hour", "0");

        JSONObject auth = new JSONObject();
        auth.set("success", true);

        result.set("list", new JSONArray());
        result.set("stats", stats);
        result.set("auth", auth);

        return result.toString();
    }
    
    /**
     * 获取邮件列表
     * @param sid 会话ID
     * @return 邮件列表的JSON字符串
     */
    public String getEmails(String sid) {
        try {
            logger.info("正在获取邮件列表，会话ID: {}", sid);

            // 解析会话ID，如果是JSON格式则提取sid_token
            String actualSid = extractSidToken(sid);
            if (actualSid == null) {
                logger.error("无效的会话ID: {}", sid);
                return createEmptyEmailList();
            }

            // 1. 先强制检查新邮件
            String forceCheckUrl = API_BASE_URL + "?f=check_email&sid_token=" + actualSid + "&seq=0";
            try {
                HttpUtils.doGet(forceCheckUrl); // 忽略响应，只是触发检查
            } catch (Exception e) {
                logger.debug("强制检查邮件失败: {}", e.getMessage());
            }

            // 2. 获取邮件列表
            String url = API_BASE_URL + "?f=get_email_list&offset=0&sid_token=" + actualSid + "&seq=0";
            String response = HttpUtils.doGet(url);

            logger.debug("主API获取邮件列表响应: {}", response);

            // 3. 如果主API失败，尝试备用API
            if (response == null || response.isEmpty() || !response.contains("list")) {
                String alternativeApiUrl = "https://www.guerrillamail.com/ajax.php";
                url = alternativeApiUrl + "?f=get_email_list&offset=0&sid_token=" + actualSid + "&seq=0";
                response = HttpUtils.doGet(url);
                logger.debug("备用API获取邮件列表响应: {}", response);
            }
            
            // 检查响应是否包含邮件列表
            if (response != null && !response.isEmpty() && JSONUtil.isTypeJSON(response)) {
                JSONObject jsonObject = JSONUtil.parseObj(response);
                
                // 如果响应包含list字段，说明是邮件列表
                if (jsonObject.containsKey("list") && jsonObject.getJSONArray("list").size() > 0) {
                    return response;
                }
                
                // 如果不包含邮件列表，或者邮件列表为空，创建一个空的邮件列表对象
                if (!jsonObject.containsKey("list")) {
                    JSONObject result = new JSONObject();
                    result.set("list", new JSONArray());
                    if (jsonObject.containsKey("stats")) {
                        result.set("stats", jsonObject.getJSONObject("stats"));
                    }
                    if (jsonObject.containsKey("auth")) {
                        result.set("auth", jsonObject.getJSONObject("auth"));
                    }
                    return result.toString();
                }
            }
            
            // 如果响应为空或者不是JSON格式，返回一个包含统计信息的最小对象
            if (response == null || response.isEmpty() || !JSONUtil.isTypeJSON(response)) {
                JSONObject result = new JSONObject();
                JSONObject stats = new JSONObject();
                stats.set("created_addresses", "0");
                stats.set("received_emails", "0");
                stats.set("total_per_hour", "0");
                
                JSONObject auth = new JSONObject();
                auth.set("success", true);
                
                result.set("list", new JSONArray());
                result.set("stats", stats);
                result.set("auth", auth);
                
                return result.toString();
            }
            
            return response;
        } catch (Exception e) {
            logger.error("获取邮件列表失败", e);
            
            // 返回一个包含错误信息的JSON
            JSONObject result = new JSONObject();
            JSONObject error = new JSONObject();
            error.set("message", e.getMessage());
            error.set("success", false);
            
            result.set("list", new JSONArray());
            result.set("error", error);
            
            return result.toString();
        }
    }
    
    /**
     * 获取邮件内容
     * @param sid 会话ID
     * @param emailId 邮件ID
     * @return 邮件内容的JSON字符串
     */
    public String getEmailContent(String sid, String emailId) {
        try {
            // 解析会话ID
            String actualSid = extractSidToken(sid);
            if (actualSid == null) {
                logger.error("无效的会话ID: {}", sid);
                JSONObject error = new JSONObject();
                error.set("message", "无效的会话ID");
                error.set("success", false);
                return error.toString();
            }

            String url = API_BASE_URL + "?f=fetch_email&email_id=" + emailId + "&sid_token=" + actualSid;
            String response = HttpUtils.doGet(url);

            if (response == null || response.isEmpty()) {
                logger.warn("获取邮件内容返回空响应");
                JSONObject error = new JSONObject();
                error.set("message", "获取邮件内容返回空响应");
                error.set("success", false);
                return error.toString();
            }

            return response;
        } catch (Exception e) {
            logger.error("获取邮件内容失败", e);

            // 返回一个包含错误信息的JSON
            JSONObject error = new JSONObject();
            error.set("message", e.getMessage());
            error.set("success", false);
            return error.toString();
        }
    }
} 
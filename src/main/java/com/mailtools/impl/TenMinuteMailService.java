package com.mailtools.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mailtools.EmailAccount;
import com.mailtools.EmailService;
import com.mailtools.util.RandomGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * 10MinuteMail临时邮箱服务实现
 * 使用10minutemail.com提供的API
 */
public class TenMinuteMailService implements EmailService {
    private static final Logger logger = LoggerFactory.getLogger(TenMinuteMailService.class);
    
    private static final String PROVIDER_NAME = "10minutemail";
    private static final String API_BASE_URL = "https://10minutemail.com";
    
    // 缓存当前会话信息
    private String currentEmailAddress = null;
    private String sessionCookie = null;
    private long sessionExpireTime = 0;
    
    @Override
    public String generateEmailAddress() {
        try {
            // 获取新的临时邮箱地址
            String url = API_BASE_URL + "/10MinuteMail/index.html";
            
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            conn.setConnectTimeout(10000);
            conn.setReadTimeout(10000);
            
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                // 从响应头获取Set-Cookie
                Map<String, java.util.List<String>> headers = conn.getHeaderFields();
                java.util.List<String> cookies = headers.get("Set-Cookie");
                if (cookies != null && !cookies.isEmpty()) {
                    sessionCookie = cookies.get(0).split(";")[0];
                }
                
                // 读取响应内容，解析邮箱地址
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
                
                String responseStr = response.toString();
                // 从HTML中提取邮箱地址
                String emailPattern = "([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})";
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(emailPattern);
                java.util.regex.Matcher matcher = pattern.matcher(responseStr);
                
                if (matcher.find()) {
                    currentEmailAddress = matcher.group(1);
                    sessionExpireTime = System.currentTimeMillis() + 600000; // 10分钟
                    logger.info("获取到10MinuteMail邮箱: {}", currentEmailAddress);
                    return currentEmailAddress;
                }
            }
            
            // 如果API失败，生成一个随机邮箱地址
            return RandomGenerator.generateEmailAddress("10minutemail.com");
        } catch (Exception e) {
            logger.error("获取10MinuteMail邮箱失败", e);
            return RandomGenerator.generateEmailAddress("10minutemail.com");
        }
    }
    
    @Override
    public EmailAccount register(String username, String password) {
        try {
            String emailAddress = generateEmailAddress();
            if (emailAddress == null) {
                return null;
            }
            
            // 10MinuteMail不需要密码，生成一个随机密码用于兼容性
            String generatedPassword = password;
            if (generatedPassword == null || generatedPassword.isEmpty()) {
                generatedPassword = RandomGenerator.generatePassword(12, true);
            }
            
            // 从邮箱地址中提取用户名
            String emailUsername = emailAddress.split("@")[0];
            
            EmailAccount account = new EmailAccount(
                    emailAddress,
                    emailUsername,
                    generatedPassword,
                    PROVIDER_NAME
            );
            
            logger.info("成功创建10MinuteMail邮箱: {}", emailAddress);
            return account;
        } catch (Exception e) {
            logger.error("创建10MinuteMail邮箱失败", e);
            return null;
        }
    }
    
    @Override
    public boolean isAvailable() {
        try {
            String url = API_BASE_URL + "/10MinuteMail/index.html";
            
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestMethod("HEAD");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            
            int responseCode = conn.getResponseCode();
            boolean available = responseCode == 200;
            logger.info("10MinuteMail服务可用性检查: {}", available);
            return available;
        } catch (Exception e) {
            logger.error("检查10MinuteMail服务可用性失败", e);
            return false;
        }
    }
    
    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }
    
    /**
     * 获取邮件列表
     * @param emailAddress 邮箱地址
     * @return 邮件列表的JSON字符串
     */
    public String getEmails(String emailAddress) {
        try {
            if (!emailAddress.equals(currentEmailAddress) || sessionCookie == null) {
                logger.warn("邮箱地址不匹配或会话已过期: {}", emailAddress);
                return "[]";
            }
            
            String url = API_BASE_URL + "/10MinuteMail/resources/messages";
            
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            conn.setRequestProperty("Cookie", sessionCookie);
            conn.setRequestProperty("Accept", "application/json");
            conn.setConnectTimeout(10000);
            conn.setReadTimeout(10000);
            
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
                
                String responseStr = response.toString();
                logger.debug("10MinuteMail邮件列表响应: {}", responseStr);
                
                // 转换为标准格式
                if (JSONUtil.isTypeJSON(responseStr)) {
                    JSONArray messages = JSONUtil.parseArray(responseStr);
                    JSONArray resultArray = new JSONArray();
                    
                    for (int i = 0; i < messages.size(); i++) {
                        JSONObject message = messages.getJSONObject(i);
                        JSONObject resultMessage = new JSONObject();
                        
                        resultMessage.set("id", message.getStr("id"));
                        resultMessage.set("from", message.getStr("sender"));
                        resultMessage.set("subject", message.getStr("subject"));
                        resultMessage.set("date", message.getStr("receivedAt"));
                        
                        resultArray.add(resultMessage);
                    }
                    
                    return resultArray.toString();
                }
            }
            
            return "[]";
        } catch (Exception e) {
            logger.error("获取10MinuteMail邮件列表失败", e);
            return "[]";
        }
    }
    
    /**
     * 获取邮件内容
     * @param emailAddress 邮箱地址
     * @param messageId 邮件ID
     * @return 邮件内容的JSON字符串
     */
    public String getEmailContent(String emailAddress, String messageId) {
        try {
            if (!emailAddress.equals(currentEmailAddress) || sessionCookie == null) {
                logger.warn("邮箱地址不匹配或会话已过期: {}", emailAddress);
                return "{}";
            }
            
            String url = API_BASE_URL + "/10MinuteMail/resources/messages/" + messageId;
            
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            conn.setRequestProperty("Cookie", sessionCookie);
            conn.setRequestProperty("Accept", "application/json");
            conn.setConnectTimeout(10000);
            conn.setReadTimeout(10000);
            
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }
                
                String responseStr = response.toString();
                logger.debug("10MinuteMail邮件内容响应: {}", responseStr);
                
                // 转换为标准格式
                if (JSONUtil.isTypeJSON(responseStr)) {
                    JSONObject message = JSONUtil.parseObj(responseStr);
                    JSONObject result = new JSONObject();
                    
                    result.set("from", message.getStr("sender"));
                    result.set("subject", message.getStr("subject"));
                    result.set("date", message.getStr("receivedAt"));
                    result.set("textBody", message.getStr("bodyPlainText"));
                    result.set("htmlBody", message.getStr("bodyHtmlContent"));
                    
                    return result.toString();
                }
            }
            
            return "{}";
        } catch (Exception e) {
            logger.error("获取10MinuteMail邮件内容失败", e);
            return "{}";
        }
    }
}

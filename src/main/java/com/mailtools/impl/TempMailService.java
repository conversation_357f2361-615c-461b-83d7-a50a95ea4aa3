package com.mailtools.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mailtools.EmailAccount;
import com.mailtools.EmailService;
import com.mailtools.util.HttpUtils;
import com.mailtools.util.RandomGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 临时邮箱服务实现类
 * 使用mail.gw提供的临时邮箱API
 * 替代已失效的1secmail服务
 */
public class TempMailService implements EmailService {
    private static final Logger logger = LoggerFactory.getLogger(TempMailService.class);

    private static final String PROVIDER_NAME = "mailgw";
    private static final String API_BASE_URL = "https://api.mail.gw";

    // 存储token和账户信息的缓存
    private String authToken = null;
    private long tokenExpireTime = 0;
    private String currentAccountId = null;
    private String currentEmailAddress = null;
    private String currentPassword = null;
    
    @Override
    public String generateEmailAddress() {
        // 获取可用域名
        String domain = getAvailableDomain();
        if (domain == null) {
            domain = "mail.gw"; // 默认域名
        }
        return RandomGenerator.generateEmailAddress(domain);
    }
    
    @Override
    public EmailAccount register(String username, String password) {
        try {
            // 获取可用域名
            String domain = getAvailableDomain();
            if (domain == null) {
                logger.error("无法获取可用域名");
                return null;
            }

            // 生成用户名和密码
            String emailUsername = username;
            if (emailUsername == null || emailUsername.isEmpty()) {
                emailUsername = RandomGenerator.generateUsername(6, 12);
            }

            String emailPassword = password;
            if (emailPassword == null || emailPassword.isEmpty()) {
                emailPassword = RandomGenerator.generatePassword(12, true);
            }

            String emailAddress = emailUsername + "@" + domain;

            // 调用mail.gw API创建账户
            JSONObject accountData = new JSONObject();
            accountData.set("address", emailAddress);
            accountData.set("password", emailPassword);

            String url = API_BASE_URL + "/accounts";
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");

            String response = HttpUtils.doPostJson(url, accountData.toString(), headers);

            if (response != null && !response.isEmpty()) {
                try {
                    JSONObject responseJson = JSONUtil.parseObj(response);
                    if (responseJson.containsKey("id")) {
                        // 保存账户信息
                        currentAccountId = responseJson.getStr("id");
                        currentEmailAddress = emailAddress;
                        currentPassword = emailPassword;

                        EmailAccount account = new EmailAccount(
                                emailAddress,
                                emailUsername,
                                emailPassword,
                                PROVIDER_NAME
                        );

                        logger.info("成功创建mail.gw邮箱: {}", emailAddress);
                        return account;
                    } else {
                        logger.error("创建账户失败，响应: {}", response);
                    }
                } catch (Exception e) {
                    logger.error("解析响应失败: {}", response, e);
                }
            }

            return null;

        } catch (Exception e) {
            logger.error("创建mail.gw邮箱失败", e);
            return null;
        }
    }
    
    /**
     * 获取可用的域名
     */
    private String getAvailableDomain() {
        try {
            String url = API_BASE_URL + "/domains";
            Map<String, String> headers = new HashMap<>();
            headers.put("Accept", "application/json");

            // 使用手动HTTP请求以确保兼容性
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection conn = (java.net.HttpURLConnection) urlObj.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("Accept", "application/json");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            conn.setConnectTimeout(10000);
            conn.setReadTimeout(10000);

            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                StringBuilder response = new StringBuilder();
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }

                String responseStr = response.toString();
                if (responseStr != null && !responseStr.isEmpty()) {
                    JSONObject jsonResponse = JSONUtil.parseObj(responseStr);
                    if (jsonResponse.containsKey("hydra:member")) {
                        JSONArray domains = jsonResponse.getJSONArray("hydra:member");
                        if (domains.size() > 0) {
                            JSONObject domain = domains.getJSONObject(0);
                            String domainName = domain.getStr("domain");
                            logger.info("获取到可用域名: {}", domainName);
                            return domainName;
                        }
                    }
                }
            } else {
                logger.warn("获取域名列表失败，状态码: {}", responseCode);
            }

            // 如果API调用失败，使用默认域名
            return "mail.gw";
        } catch (Exception e) {
            logger.error("获取域名列表失败", e);
            return "mail.gw"; // 返回默认域名
        }
    }
    
    /**
     * 获取身份验证令牌
     */
    private String getAuthToken(String email, String password) {
        try {
            // 如果token未过期，直接返回
            if (authToken != null && System.currentTimeMillis() < tokenExpireTime) {
                return authToken;
            }

            // 创建登录请求
            JSONObject loginData = new JSONObject();
            loginData.set("address", email);
            loginData.set("password", password);

            String url = API_BASE_URL + "/token";

            // 使用手动HTTP请求
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection conn = (java.net.HttpURLConnection) urlObj.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("Accept", "application/json");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            conn.setDoOutput(true);
            conn.setConnectTimeout(10000);
            conn.setReadTimeout(10000);

            // 写入请求体
            try (java.io.OutputStream os = conn.getOutputStream()) {
                byte[] input = loginData.toString().getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                StringBuilder response = new StringBuilder();
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }

                String responseStr = response.toString();
                if (responseStr != null && !responseStr.isEmpty()) {
                    JSONObject responseJson = JSONUtil.parseObj(responseStr);
                    if (responseJson.containsKey("token")) {
                        // 保存token
                        authToken = responseJson.getStr("token");
                        // token有效期设为1小时
                        tokenExpireTime = System.currentTimeMillis() + 3600000;
                        logger.info("成功获取认证令牌");
                        return authToken;
                    }
                }
            } else {
                logger.warn("获取令牌失败，状态码: {}", responseCode);
            }

            return null;
        } catch (Exception e) {
            logger.error("获取身份验证令牌失败", e);
            return null;
        }
    }
    
    @Override
    public boolean isAvailable() {
        try {
            // 检查mail.gw API是否可用
            String url = API_BASE_URL + "/domains";

            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection conn = (java.net.HttpURLConnection) urlObj.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("Accept", "application/json");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);

            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                StringBuilder response = new StringBuilder();
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(conn.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }

                String responseStr = response.toString();
                boolean available = responseStr != null && !responseStr.isEmpty() && responseStr.contains("hydra:member");
                logger.info("mail.gw服务可用性检查: {}", available);
                return available;
            } else {
                logger.warn("mail.gw服务不可用，状态码: {}", responseCode);
                return false;
            }
        } catch (Exception e) {
            logger.error("检查mail.gw服务可用性时发生错误", e);
            return false;
        }
    }
    
    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }
    
    /**
     * 获取邮箱中的邮件列表
     * @param emailAddress 邮箱地址
     * @return 邮件列表的JSON字符串
     */
    public String getEmails(String emailAddress) {
        try {
            String[] parts = emailAddress.split("@");
            if (parts.length != 2) {
                logger.error("邮箱地址格式不正确: {}", emailAddress);
                return "[]";
            }
            
            // 对于mail.gw，需要先获取token
            String password = findPasswordForEmail(emailAddress);
            if (password == null) {
                logger.error("找不到邮箱密码: {}", emailAddress);
                return "[]";
            }
            
            String token = getAuthToken(emailAddress, password);
            if (token == null) {
                logger.error("获取身份验证令牌失败");
                return "[]";
            }
            
            // 获取邮件列表
            String url = API_BASE_URL + "/messages";
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + token);
            
            // 修改调用方式，使用正确的参数
            String response = null;
            try {
                // 手动构建带Authorization头的URL连接
                java.net.URL urlObj = new java.net.URL(url);
                java.net.HttpURLConnection conn = (java.net.HttpURLConnection) urlObj.openConnection();
                conn.setRequestMethod("GET");
                conn.setRequestProperty("Authorization", "Bearer " + token);
                conn.setRequestProperty("Accept", "application/json");
                
                // 读取响应
                java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(conn.getInputStream(), "UTF-8"));
                StringBuilder responseBuilder = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    responseBuilder.append(line);
                }
                reader.close();
                
                response = responseBuilder.toString();
            } catch (Exception e) {
                logger.error("获取邮件列表时发生网络错误", e);
            }
            
            if (response == null || response.isEmpty()) {
                logger.warn("获取邮件列表返回空响应");
                return "[]";
            }
            
            // 转换响应格式为兼容1secmail的格式
            JSONObject jsonResponse = JSONUtil.parseObj(response);
            if (jsonResponse.containsKey("hydra:member")) {
                JSONArray messages = jsonResponse.getJSONArray("hydra:member");
                JSONArray resultArray = new JSONArray();
                
                for (int i = 0; i < messages.size(); i++) {
                    JSONObject message = messages.getJSONObject(i);
                    JSONObject resultMessage = new JSONObject();
                    
                    resultMessage.set("id", message.getStr("id"));
                    resultMessage.set("from", message.getStr("from"));
                    resultMessage.set("subject", message.getStr("subject"));
                    resultMessage.set("date", message.getStr("createdAt"));
                    
                    resultArray.add(resultMessage);
                }
                
                return resultArray.toString();
            }
            
            return "[]";
        } catch (Exception e) {
            logger.error("获取邮件列表失败", e);
            return "[]";
        }
    }
    
    /**
     * 查找邮箱对应的密码
     * 使用当前缓存的密码信息
     */
    private String findPasswordForEmail(String emailAddress) {
        // 如果是当前创建的邮箱，返回缓存的密码
        if (emailAddress != null && emailAddress.equals(currentEmailAddress) && currentPassword != null) {
            return currentPassword;
        }

        // 对于其他邮箱，无法获取密码
        logger.warn("无法找到邮箱 {} 的密码", emailAddress);
        return null;
    }
    
    /**
     * 获取指定邮件的内容
     * @param emailAddress 邮箱地址
     * @param messageId 邮件ID
     * @return 邮件内容的JSON字符串
     */
    public String getEmailContent(String emailAddress, String messageId) {
        try {
            String[] parts = emailAddress.split("@");
            if (parts.length != 2) {
                return "{}";
            }
            
            // 对于mail.gw，需要先获取token
            String password = findPasswordForEmail(emailAddress);
            if (password == null) {
                logger.error("找不到邮箱密码: {}", emailAddress);
                return "{}";
            }
            
            String token = getAuthToken(emailAddress, password);
            if (token == null) {
                logger.error("获取身份验证令牌失败");
                return "{}";
            }
            
            // 获取邮件内容
            String url = API_BASE_URL + "/messages/" + messageId;
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + token);
            
            // 修改调用方式，使用正确的参数
            String response = null;
            try {
                // 手动构建带Authorization头的URL连接
                java.net.URL urlObj = new java.net.URL(url);
                java.net.HttpURLConnection conn = (java.net.HttpURLConnection) urlObj.openConnection();
                conn.setRequestMethod("GET");
                conn.setRequestProperty("Authorization", "Bearer " + token);
                conn.setRequestProperty("Accept", "application/json");
                
                // 读取响应
                java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(conn.getInputStream(), "UTF-8"));
                StringBuilder responseBuilder = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    responseBuilder.append(line);
                }
                reader.close();
                
                response = responseBuilder.toString();
            } catch (Exception e) {
                logger.error("获取邮件内容时发生网络错误", e);
            }
            
            if (response == null || response.isEmpty()) {
                logger.warn("获取邮件内容返回空响应");
                return "{}";
            }
            
            // 转换响应格式为兼容1secmail的格式
            JSONObject message = JSONUtil.parseObj(response);
            JSONObject result = new JSONObject();
            
            result.set("from", message.getStr("from"));
            result.set("subject", message.getStr("subject"));
            result.set("date", message.getStr("createdAt"));
            result.set("textBody", message.getStr("text"));
            result.set("htmlBody", message.getStr("html"));
            
            return result.toString();
        } catch (Exception e) {
            logger.error("获取邮件内容失败", e);
            return "{}";
        }
    }
} 